<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0-windows</TargetFramework>
    <AssemblyName>HslCommunication</AssemblyName>
    <RootNamespace>HslCommunication</RootNamespace>
    <AssemblyVersion>12.3.3.0</AssemblyVersion>
    <FileVersion>12.3.3.0</FileVersion>
    <LangVersion>latest</LangVersion>
    <UseWindowsForms>true</UseWindowsForms>
    <GenerateResourceUsePreserializedResources>true</GenerateResourceUsePreserializedResources>
    <GenerateResourceWarnOnBinaryFormatterUse>false</GenerateResourceWarnOnBinaryFormatterUse>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="System.Management" Version="8.0.0" />
    <PackageReference Include="System.Resources.Extensions" Version="8.0.0" />
    <PackageReference Include="System.IO.Ports" Version="8.0.0" />
    <PackageReference Include="System.Data.SqlClient" Version="4.8.6" />
    <PackageReference Include="System.Drawing.Common" Version="8.0.8" />
  </ItemGroup>


</Project>