﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.IDCard.IdentityCard
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System;
using System.Text;


namespace HslCommunication.Profinet.IDCard;


/// <summary>身份证的信息类</summary>
public class IdentityCard
{
  /// <summary>名字</summary>
  public string Name { get; set; }

  /// <summary>性别</summary>
  public string Sex { get; set; }

  /// <summary>身份证号</summary>
  public string Id { get; set; }

  /// <summary>民族</summary>
  public string Nation { get; set; }

  /// <summary>生日</summary>
  public DateTime Birthday { get; set; }

  /// <summary>地址</summary>
  public string Address { get; set; }

  /// <summary>发证机关</summary>
  public string Organ { get; set; }

  /// <summary>有效期日期的起始日期</summary>
  public DateTime ValidityStartDate { get; set; }

  /// <summary>有效期日期的结束日期</summary>
  public DateTime ValidityEndDate { get; set; }

  /// <summary>头像信息</summary>
  public byte[] Portrait { get; set; }

  /// <summary>返回表示当前对象的字符串</summary>
  /// <returns>字符串</returns>
  public override string ToString()
  {
    StringBuilder stringBuilder1 = new StringBuilder();
    stringBuilder1.Append("姓名：" + this.Name);
    stringBuilder1.Append(Environment.NewLine);
    stringBuilder1.Append("性别：" + this.Sex);
    stringBuilder1.Append(Environment.NewLine);
    stringBuilder1.Append("民族：" + this.Nation);
    stringBuilder1.Append(Environment.NewLine);
    stringBuilder1.Append("身份证号：" + this.Id);
    stringBuilder1.Append(Environment.NewLine);
    StringBuilder stringBuilder2 = stringBuilder1;
    DateTime birthday = this.Birthday;
    // ISSUE: variable of a boxed type
    __Boxed<int> year = (ValueType) birthday.Year;
    birthday = this.Birthday;
    // ISSUE: variable of a boxed type
    __Boxed<int> month = (ValueType) birthday.Month;
    birthday = this.Birthday;
    // ISSUE: variable of a boxed type
    __Boxed<int> day = (ValueType) birthday.Day;
    string str = $"出身日期：{year}年{month}月{day}日";
    stringBuilder2.Append(str);
    stringBuilder1.Append(Environment.NewLine);
    stringBuilder1.Append("地址：" + this.Address);
    stringBuilder1.Append(Environment.NewLine);
    stringBuilder1.Append("发证机关：" + this.Organ);
    stringBuilder1.Append(Environment.NewLine);
    stringBuilder1.Append($"有效日期：{this.ValidityStartDate.Year}年{this.ValidityStartDate.Month}月{this.ValidityStartDate.Day}日 - {this.ValidityEndDate.Year}年{this.ValidityEndDate.Month}月{this.ValidityEndDate.Day}日");
    stringBuilder1.Append(Environment.NewLine);
    return stringBuilder1.ToString();
  }
}
