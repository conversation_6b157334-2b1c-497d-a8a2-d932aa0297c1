using System.Reflection;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;

// [assembly: Extension] // 移除此行，因为在 .NET 8 中不需要显式声明
[assembly: AssemblyTitle("HslCommunication")]
[assembly: AssemblyDescription("一个工业物联网的底层架构框架，专注于底层的技术通信及跨平台，跨语言通信功能，实现各种主流的PLC数据读写，实现modbus的各种协议读写等等，支持快速搭建工业上位机软件，组态软件，SCADA软件，工厂MES系统，助力企业工业4.0腾飞，实现智能制造，智慧工厂的目标。")]
[assembly: AssemblyConfiguration("")]
[assembly: AssemblyCompany("杭州胡工物联科技有限公司")]
[assembly: AssemblyProduct("HslCommunication")]
[assembly: AssemblyCopyright("Copyright © By Richard.Hu 2017-2025")]
[assembly: AssemblyTrademark("")]
[assembly: ComVisible(false)]
[assembly: Guid("7f8029da-1b94-4b1d-b91f-5b21dbf2f3a2")]
[assembly: AssemblyFileVersion("********")]
[assembly: AssemblyVersion("********")]
